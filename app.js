import express from 'express';
import cors from 'cors';
import { PORT } from './config/env.js';
import cookieParser from 'cookie-parser';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Debug: Log current working directory and file paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
console.log('🔍 Current working directory:', process.cwd());
console.log('🔍 App.js directory:', __dirname);
console.log('🔍 Routes directory:', join(__dirname, 'routes'));

import userRouter from './routes/user.routes.js';
import authRouter from './routes/auth.routes.js';
import subscriptionRouter from './routes/subscription.routes.js';
import wordpressRouter from './routes/wordpress.routes.js';
import connectToDatabase from './database/mongodb.js';
import errorMiddleware from './middleware/error.middleware.js';
import arcjetMiddleware from './middleware/arcjet.middleware.js';
import workflowRouter from './routes/workflow.routes.js';





const app = express();

// Dynamic CORS configuration with automatic Vercel URL support
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        // List of allowed origins
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:5173',
            'http://localhost:5174',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:5173',
            'http://127.0.0.1:5174',
        ];

        // Check if origin is in allowed list
        if (allowedOrigins.includes(origin)) {
            return callback(null, true);
        }

        // Dynamic Vercel pattern matching for our projects
        const vercelPatterns = [
            // AI Crawler Frontend - matches any deployment
            /^https:\/\/ai-crawler-frontend-[a-z0-9]+-suhailult777s-projects\.vercel\.app$/,
            // WordPress Plugin Distribution
            /^https:\/\/wp-plugin-distribution-[a-z0-9]+-suhailult777s-projects\.vercel\.app$/,
            // Generic pattern for any of our Vercel projects
            /^https:\/\/[a-z0-9-]+-suhailult777s-projects\.vercel\.app$/
        ];

        // Check if origin matches any Vercel pattern
        const isVercelAllowed = vercelPatterns.some(pattern => pattern.test(origin));
        if (isVercelAllowed) {
            console.log('✅ CORS allowed for Vercel origin:', origin);
            return callback(null, true);
        }

        // Log rejected origins for debugging
        console.log('🚫 CORS rejected origin:', origin);
        callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
};

app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

app.use(arcjetMiddleware);

app.use('/api/v1/auth', authRouter); // we are using use for middleware
app.use('/api/v1/users', userRouter); // we are using use for middleware
app.use('/api/v1/subscriptions', subscriptionRouter); // we are using use for middleware
app.use('/api/v1/wordpress', wordpressRouter); // WordPress plugin routes
app.use('/api/v1/workflows', workflowRouter); // we are using use for middleware

app.use(errorMiddleware);


app.get('/', (req, res) => {
    res.send('welcome to the subscription tracker api');
})  //path, callback function

app.get('/api/v1', (req, res) => {
    res.json({
        message: 'AI Crawler Guard API v1',
        status: 'running',
        timestamp: new Date().toISOString(),
        origin: req.get('Origin') || 'No origin header',
        endpoints: {
            auth: '/api/v1/auth',
            users: '/api/v1/users',
            subscriptions: '/api/v1/subscriptions',
            wordpress: '/api/v1/wordpress',
            workflows: '/api/v1/workflows'
        }
    });
});

// CORS debug endpoint
app.get('/api/v1/cors-test', (req, res) => {
    res.json({
        message: 'CORS test successful',
        origin: req.get('Origin') || 'No origin header',
        timestamp: new Date().toISOString(),
        headers: req.headers
    });
});

app.listen(PORT, async () => {

    console.log(`Subscription Tracker API is running on http://localhost:${PORT}`);

    await connectToDatabase()

})

export default app;