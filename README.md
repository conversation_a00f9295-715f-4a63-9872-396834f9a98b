# AI Crawler Blocker Backend

A Node.js backend service for managing AI crawler detection and subscription tracking.

## Features

- User authentication and management
- WordPress site integration
- Subscription management
- Bot detection and blocking
- Email notifications
- Analytics tracking

## Tech Stack

- Node.js
- Express.js
- MongoDB
- DrizzleORM
- Arcjet (for bot protection)
- Nodemailer (for emails)
- Upstash (for caching)

## Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Configure environment variables
4. Run the server: `npm start`

## API Endpoints

- `/api/auth` - Authentication routes
- `/api/users` - User management
- `/api/subscriptions` - Subscription handling
- `/api/wordpress` - WordPress integration
- `/api/workflows` - Workflow management

## Environment Variables

Make sure to set up the required environment variables in your `.env` file.
