// Test script to verify WordPress endpoints are working
const PRODUCTION_API_URL = 'https://ai-crawler-blocker-backend-2-1.onrender.com/api/v1';
const TEST_USER = {
    email: '<EMAIL>',
    password: 'testpassword123'
};

console.log('🧪 Testing WordPress Endpoints on Production');
console.log('📍 API URL:', PRODUCTION_API_URL);
console.log('=' .repeat(60));

async function testWordPressEndpoints() {
    let authToken = null;

    try {
        // Step 1: Authenticate to get token
        console.log('\n1️⃣ Authenticating user...');
        const signInResponse = await fetch(`${PRODUCTION_API_URL}/auth/sign-in`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(TEST_USER)
        });

        const signInData = await signInResponse.json();
        
        if (signInResponse.ok) {
            console.log('✅ Authentication successful');
            authToken = signInData.data.token;
            console.log('🔑 Token obtained');
        } else {
            console.log('❌ Authentication failed:', signInData);
            return;
        }

        // Step 2: Test WordPress sites endpoint (GET)
        console.log('\n2️⃣ Testing GET /wordpress/sites...');
        const sitesResponse = await fetch(`${PRODUCTION_API_URL}/wordpress/sites`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            }
        });

        const sitesData = await sitesResponse.json();
        
        if (sitesResponse.ok) {
            console.log('✅ WordPress sites endpoint working');
            console.log('📊 Sites found:', sitesData.data ? sitesData.data.length : 0);
            if (sitesData.data && sitesData.data.length > 0) {
                console.log('📝 First site:', sitesData.data[0].url || sitesData.data[0].name);
            }
        } else {
            console.log('❌ WordPress sites endpoint failed:', sitesData);
        }

        // Step 3: Test WordPress site creation (POST)
        console.log('\n3️⃣ Testing POST /wordpress/sites...');
        const testSite = {
            url: 'https://test-wordpress-site.example.com',
            name: 'Test WordPress Site'
        };

        const createSiteResponse = await fetch(`${PRODUCTION_API_URL}/wordpress/sites`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testSite)
        });

        const createSiteData = await createSiteResponse.json();
        
        if (createSiteResponse.ok) {
            console.log('✅ WordPress site creation working');
            console.log('📝 Site created:', createSiteData.data.url);
            console.log('🔑 API Key generated:', createSiteData.data.apiKey ? 'Yes' : 'No');
        } else {
            console.log('❌ WordPress site creation failed:', createSiteData);
            
            // Check if it's a duplicate site error (which is expected)
            if (createSiteResponse.status === 409 || createSiteData.message?.includes('already exists')) {
                console.log('ℹ️  Site already exists (this is expected for testing)');
            }
        }

        // Step 4: Test WordPress analytics endpoint
        console.log('\n4️⃣ Testing WordPress analytics endpoints...');
        
        // First, get a site ID to test analytics
        const sitesForAnalytics = await fetch(`${PRODUCTION_API_URL}/wordpress/sites`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            }
        });

        const sitesAnalyticsData = await sitesForAnalytics.json();
        
        if (sitesAnalyticsData.data && sitesAnalyticsData.data.length > 0) {
            const siteId = sitesAnalyticsData.data[0].id;
            console.log('📊 Testing analytics for site ID:', siteId);

            // Test analytics endpoint
            const analyticsResponse = await fetch(`${PRODUCTION_API_URL}/wordpress/sites/${siteId}/analytics`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                }
            });

            const analyticsData = await analyticsResponse.json();
            
            if (analyticsResponse.ok) {
                console.log('✅ WordPress analytics endpoint working');
                console.log('📈 Analytics data received');
            } else {
                console.log('❌ WordPress analytics endpoint failed:', analyticsData);
            }
        } else {
            console.log('ℹ️  No sites available for analytics testing');
        }

        console.log('\n' + '=' .repeat(60));
        console.log('🎉 WordPress Endpoints Test Complete!');
        console.log('✅ All critical WordPress functionality is working');

    } catch (error) {
        console.error('\n❌ Test failed with error:', error.message);
        console.error('🔍 Full error:', error);
    }
}

// Run the test
testWordPressEndpoints();
