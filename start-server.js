#!/usr/bin/env node

/**
 * AI Crawler Guard - Server Startup Script
 * This script ensures proper environment setup before starting the server
 */

import { existsSync } from 'fs';
import { spawn } from 'child_process';
import { join } from 'path';

console.log('🚀 AI Crawler Guard - Starting Server...\n');

// Check Node.js version
const nodeVersion = process.version;
console.log(`📋 Node.js Version: ${nodeVersion}`);

// Set default NODE_ENV if not set
if (!process.env.NODE_ENV) {
    process.env.NODE_ENV = 'development';
    console.log(`🔧 NODE_ENV not set, defaulting to: ${process.env.NODE_ENV}`);
} else {
    console.log(`🔧 NODE_ENV: ${process.env.NODE_ENV}`);
}

// Check for environment files
const envFiles = [
    `.env.${process.env.NODE_ENV}.local`,
    `.env.local`,
    `.env.${process.env.NODE_ENV}`,
    `.env`
];

console.log('\n📁 Checking for environment files:');
let foundEnvFile = false;
for (const envFile of envFiles) {
    const exists = existsSync(envFile);
    console.log(`   ${exists ? '✅' : '❌'} ${envFile}`);
    if (exists && !foundEnvFile) {
        foundEnvFile = true;
        console.log(`   🎯 Will use: ${envFile}`);
    }
}

if (!foundEnvFile) {
    console.log('\n⚠️  No environment file found! Creating default .env file...');
    
    // Create a basic .env file
    const defaultEnv = `# AI Crawler Guard - Auto-generated Environment File
NODE_ENV=${process.env.NODE_ENV}
PORT=3000
DB_URI="postgresql://ai-crawler-guard_owner:<EMAIL>/ai-crawler-guard?sslmode=require"
JWT_SECRET="auto_generated_jwt_secret_${Date.now()}"
JWT_EXPIRES_IN="1d"
ARCJET_KEY="ajkey_01jv2k26vvercv3v13k2b9kn1d"
ARCJET_ENV="${process.env.NODE_ENV}"
EMAIL_PASSWORD="ysbb ohjv ljbt ttcm"
SERVER_URL="http://localhost:3000"
`;

    try {
        const fs = await import('fs');
        fs.writeFileSync('.env', defaultEnv);
        console.log('✅ Created default .env file');
    } catch (error) {
        console.error('❌ Failed to create .env file:', error.message);
    }
}

// Check if package.json exists
if (!existsSync('package.json')) {
    console.error('\n❌ package.json not found! Make sure you\'re in the correct directory.');
    process.exit(1);
}

// Check if node_modules exists
if (!existsSync('node_modules')) {
    console.log('\n📦 node_modules not found. Installing dependencies...');
    const installProcess = spawn('pnpm', ['install'], { 
        stdio: 'inherit',
        shell: true 
    });
    
    installProcess.on('close', (code) => {
        if (code === 0) {
            console.log('✅ Dependencies installed successfully');
            startServer();
        } else {
            console.error('❌ Failed to install dependencies');
            process.exit(1);
        }
    });
} else {
    startServer();
}

function startServer() {
    console.log('\n🚀 Starting the server...\n');
    
    // Start the main application
    const serverProcess = spawn('node', ['app.js'], {
        stdio: 'inherit',
        env: { ...process.env }
    });
    
    serverProcess.on('close', (code) => {
        console.log(`\n🔄 Server process exited with code ${code}`);
        if (code !== 0) {
            console.log('🔧 Attempting to restart...');
            setTimeout(startServer, 2000);
        }
    });
    
    serverProcess.on('error', (error) => {
        console.error('❌ Failed to start server:', error.message);
        process.exit(1);
    });
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down gracefully...');
        serverProcess.kill('SIGINT');
        process.exit(0);
    });
    
    process.on('SIGTERM', () => {
        console.log('\n🛑 Received SIGTERM, shutting down...');
        serverProcess.kill('SIGTERM');
        process.exit(0);
    });
}
