import { config } from 'dotenv';

// Load environment variables
config({ path: '.env' });

const API_BASE_URL = 'http://localhost:3000';
const TEST_USER = {
    name: 'Test User Auth',
    email: '<EMAIL>',
    password: 'testpassword123'
};

console.log('🧪 Testing AI Crawler Guard Authentication Flow');
console.log('📍 API Base URL:', API_BASE_URL);
console.log('👤 Test User:', TEST_USER.email);
console.log('='.repeat(60));

async function testAuthFlow() {
    let authToken = null;
    let userId = null;

    try {
        // Test 1: Health Check
        console.log('\n1️⃣ Testing API Health Check...');
        const healthResponse = await fetch(`${API_BASE_URL}/api/v1`);
        const healthData = await healthResponse.json();

        if (healthResponse.ok) {
            console.log('✅ API is running:', healthData.message);
        } else {
            console.log('❌ API health check failed:', healthData);
            return;
        }

        // Test 2: Sign Up
        console.log('\n2️⃣ Testing User Sign Up...');
        const signUpResponse = await fetch(`${API_BASE_URL}/api/v1/auth/sign-up`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(TEST_USER)
        });

        const signUpData = await signUpResponse.json();

        if (signUpResponse.ok) {
            console.log('✅ Sign up successful');
            console.log('📝 User created:', signUpData.data.user.name);
            console.log('🔑 Token received:', signUpData.data.token ? 'Yes' : 'No');
            authToken = signUpData.data.token;
            userId = signUpData.data.user.id;

            // Verify password is not in response
            if (signUpData.data.user.password) {
                console.log('⚠️  WARNING: Password field present in response!');
            } else {
                console.log('✅ Password field properly excluded from response');
            }
        } else {
            if (signUpResponse.status === 409) {
                console.log('ℹ️  User already exists, proceeding to sign in...');
            } else {
                console.log('❌ Sign up failed:', signUpData);
                return;
            }
        }

        // Test 3: Sign In
        console.log('\n3️⃣ Testing User Sign In...');
        const signInResponse = await fetch(`${API_BASE_URL}/api/v1/auth/sign-in`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: TEST_USER.email,
                password: TEST_USER.password
            })
        });

        const signInData = await signInResponse.json();

        if (signInResponse.ok) {
            console.log('✅ Sign in successful');
            console.log('📝 User:', signInData.data.user.name);
            console.log('🔑 Token received:', signInData.data.token ? 'Yes' : 'No');
            authToken = signInData.data.token;
            userId = signInData.data.user.id;

            // Verify password is not in response
            if (signInData.data.user.password) {
                console.log('⚠️  WARNING: Password field present in response!');
            } else {
                console.log('✅ Password field properly excluded from response');
            }
        } else {
            console.log('❌ Sign in failed:', signInData);
            return;
        }

        // Test 4: Token Validation (Protected Route)
        console.log('\n4️⃣ Testing Token Validation...');
        const userResponse = await fetch(`${API_BASE_URL}/api/v1/users/${userId}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            }
        });

        const userData = await userResponse.json();

        if (userResponse.ok) {
            console.log('✅ Token validation successful');
            console.log('📝 User data retrieved:', userData.data.name);

            // Verify password is not in response
            if (userData.data.password) {
                console.log('⚠️  WARNING: Password field present in protected route response!');
            } else {
                console.log('✅ Password field properly excluded from protected route');
            }
        } else {
            console.log('❌ Token validation failed:', userData);
        }

        // Test 5: Invalid Token
        console.log('\n5️⃣ Testing Invalid Token Handling...');
        const invalidTokenResponse = await fetch(`${API_BASE_URL}/api/v1/users/${userId}`, {
            headers: {
                'Authorization': 'Bearer invalid_token_here',
                'Content-Type': 'application/json',
            }
        });

        const invalidTokenData = await invalidTokenResponse.json();

        if (invalidTokenResponse.status === 401) {
            console.log('✅ Invalid token properly rejected');
            console.log('📝 Error message:', invalidTokenData.message);
        } else {
            console.log('❌ Invalid token not properly handled:', invalidTokenData);
        }

        // Test 6: Missing Token
        console.log('\n6️⃣ Testing Missing Token Handling...');
        const noTokenResponse = await fetch(`${API_BASE_URL}/api/v1/users/${userId}`, {
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const noTokenData = await noTokenResponse.json();

        if (noTokenResponse.status === 401) {
            console.log('✅ Missing token properly rejected');
            console.log('📝 Error message:', noTokenData.message);
        } else {
            console.log('❌ Missing token not properly handled:', noTokenData);
        }

        // Test 7: Invalid Credentials
        console.log('\n7️⃣ Testing Invalid Credentials...');
        const invalidCredsResponse = await fetch(`${API_BASE_URL}/api/v1/auth/sign-in`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: TEST_USER.email,
                password: 'wrongpassword'
            })
        });

        const invalidCredsData = await invalidCredsResponse.json();

        if (invalidCredsResponse.status === 401) {
            console.log('✅ Invalid credentials properly rejected');
            console.log('📝 Error message:', invalidCredsData.message);
        } else {
            console.log('❌ Invalid credentials not properly handled:', invalidCredsData);
        }

        console.log('\n' + '='.repeat(60));
        console.log('🎉 Authentication Flow Test Complete!');
        console.log('✅ All critical authentication features are working properly');

    } catch (error) {
        console.error('\n❌ Test failed with error:', error.message);
        console.error('🔍 Full error:', error);
    }
}

// Run the test
testAuthFlow();
