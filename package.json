{"name": "subdub", "version": "0.0.0", "private": true, "type": "module", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "dev:safe": "node start-server.js", "server": "node start-server.js", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@arcjet/inspect": "^1.0.0-beta.7", "@arcjet/node": "^1.0.0-beta.7", "@upstash/workflow": "^0.2.13", "bcryptjs": "^3.0.2", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "dayjs": "^1.11.13", "debug": "~2.6.9", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "express": "~4.16.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "morgan": "~1.9.1", "nodemailer": "^7.0.3", "pg": "^8.16.3", "postgres": "^3.4.7", "subdub": "file:", "subscription-tracker": "file:"}, "devDependencies": {"@eslint/js": "^9.26.0", "eslint": "^9.26.0", "globals": "^16.1.0", "nodemon": "^3.1.10"}}