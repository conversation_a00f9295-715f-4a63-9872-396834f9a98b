// Test script to verify production authentication fixes
const PRODUCTION_API_URL = 'https://ai-crawler-blocker-backend-2-1.onrender.com/api/v1';
const TEST_USER = {
    name: 'Production Test User',
    email: '<EMAIL>',
    password: 'testpassword123'
};

console.log('🧪 Testing Production Authentication Fixes');
console.log('📍 API URL:', PRODUCTION_API_URL);
console.log('👤 Test User:', TEST_USER.email);
console.log('=' .repeat(60));

async function testProductionAuth() {
    try {
        // Test 1: Health Check
        console.log('\n1️⃣ Testing API Health Check...');
        const healthResponse = await fetch(`${PRODUCTION_API_URL}`);
        const healthData = await healthResponse.json();
        
        if (healthResponse.ok) {
            console.log('✅ API is running:', healthData.message);
        } else {
            console.log('❌ API health check failed:', healthData);
            return;
        }

        // Test 2: Sign Up (to check if password is excluded from response)
        console.log('\n2️⃣ Testing User Sign Up...');
        const signUpResponse = await fetch(`${PRODUCTION_API_URL}/auth/sign-up`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(TEST_USER)
        });

        const signUpData = await signUpResponse.json();
        
        if (signUpResponse.ok) {
            console.log('✅ Sign up successful');
            console.log('📝 User created:', signUpData.data.user.name);
            console.log('🔑 Token received:', signUpData.data.token ? 'Yes' : 'No');
            
            // Check if password is excluded (this is the key test)
            if (signUpData.data.user.password) {
                console.log('❌ CRITICAL: Password field still present in response!');
                console.log('   This means the fixes are NOT deployed yet');
                return false;
            } else {
                console.log('✅ Password field properly excluded from response');
                console.log('   ✨ Authentication fixes are DEPLOYED!');
                return true;
            }
        } else {
            if (signUpResponse.status === 409) {
                console.log('ℹ️  User already exists, testing sign in...');
                
                // Test Sign In instead
                console.log('\n3️⃣ Testing User Sign In...');
                const signInResponse = await fetch(`${PRODUCTION_API_URL}/auth/sign-in`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: TEST_USER.email,
                        password: TEST_USER.password
                    })
                });

                const signInData = await signInResponse.json();
                
                if (signInResponse.ok) {
                    console.log('✅ Sign in successful');
                    console.log('📝 User:', signInData.data.user.name);
                    console.log('🔑 Token received:', signInData.data.token ? 'Yes' : 'No');
                    
                    // Check if password is excluded (this is the key test)
                    if (signInData.data.user.password) {
                        console.log('❌ CRITICAL: Password field still present in response!');
                        console.log('   This means the fixes are NOT deployed yet');
                        return false;
                    } else {
                        console.log('✅ Password field properly excluded from response');
                        console.log('   ✨ Authentication fixes are DEPLOYED!');
                        return true;
                    }
                } else {
                    console.log('❌ Sign in failed:', signInData);
                    
                    // Check if this is the old error message
                    if (signInData.error && signInData.error.includes('User.findById(...).select is not a function')) {
                        console.log('❌ CRITICAL: Old .select() error detected!');
                        console.log('   The fixes are NOT deployed yet');
                        return false;
                    }
                }
            } else {
                console.log('❌ Sign up failed:', signUpData);
                
                // Check if this is the old error message
                if (signUpData.error && signUpData.error.includes('User.findById(...).select is not a function')) {
                    console.log('❌ CRITICAL: Old .select() error detected!');
                    console.log('   The fixes are NOT deployed yet');
                    return false;
                }
            }
        }

    } catch (error) {
        console.error('\n❌ Test failed with error:', error.message);
        
        if (error.message.includes('User.findById(...).select is not a function')) {
            console.log('❌ CRITICAL: Old .select() error detected!');
            console.log('   The fixes are NOT deployed yet');
            return false;
        }
        
        return false;
    }
}

// Run the test
testProductionAuth().then(success => {
    console.log('\n' + '=' .repeat(60));
    if (success) {
        console.log('🎉 PRODUCTION AUTHENTICATION FIXES DEPLOYED SUCCESSFULLY!');
        console.log('✅ Password exclusion working');
        console.log('✅ Drizzle ORM fixes deployed');
        console.log('✅ Ready for frontend connection testing');
    } else {
        console.log('⏳ PRODUCTION DEPLOYMENT STILL IN PROGRESS...');
        console.log('❌ Old code still running on Render');
        console.log('🔄 Wait for Render auto-deployment to complete');
        console.log('⏰ This usually takes 2-5 minutes after git push');
    }
});
